import 'package:pocketbase/pocketbase.dart';
import '../../../../core/services/logger_service.dart';

class ContentItemModel {
  final String id;
  final String collectionId;
  final String collectionName;
  final DateTime created;
  final DateTime updated;
  final String type;
  final String title;
  final String slug;
  final String? summary;
  final String? bodyContent;
  final String? mediaUrl;
  final String? thumbnailImage; // Filename for image
  final String? mediaFile; // Filename for audio/video
  final String? authorId;
  final DateTime? publishedAt;
  final String status;
  final List<int> targetUserLevels;
  final List<String> tags;
  final int analyticsViews;

  ContentItemModel({
    required this.id,
    required this.collectionId,
    required this.collectionName,
    required this.created,
    required this.updated,
    required this.type,
    required this.title,
    required this.slug,
    this.summary,
    this.bodyContent,
    this.mediaUrl,
    this.thumbnailImage,
    this.mediaFile,
    this.authorId,
    this.publishedAt,
    required this.status,
    required this.targetUserLevels,
    required this.tags,
    required this.analyticsViews,
  });

  factory ContentItemModel.fromJson(Map<String, dynamic> json) {
    return ContentItemModel(
      id: json['id'] as String,
      collectionId: json['collectionId'] as String,
      collectionName: json['collectionName'] as String,
      created: _parseDateTime(json['created']) ?? DateTime.now(),
      updated: _parseDateTime(json['updated']) ?? DateTime.now(),
      type: json['type'] as String,
      title: json['title'] as String,
      slug: json['slug'] as String,
      summary: json['summary'] as String?,
      bodyContent: json['body_content'] as String?,
      mediaUrl:
          json['media_url']
              as String?, // Keep for potential other uses? Or remove if only media_file is used. Let's keep for now.
      thumbnailImage: json['thumbnail_image'] as String?,
      mediaFile: json['media_file'] as String?, // Add media_file
      authorId: json['author_id'] as String?,
      publishedAt:
          json['published_at'] != null &&
                  (json['published_at'] as String).isNotEmpty
              ? DateTime.tryParse(json['published_at'] as String)
              : null,
      status: json['status'] as String,
      targetUserLevels:
          (json['target_user_levels'] as List<dynamic>?)
              ?.map((e) {
                if (e is int) return e;
                if (e is String) return int.tryParse(e);
                return null; // Or handle other types as needed
              })
              .where((e) => e != null) // Filter out nulls from failed parsing
              .cast<int>() // Ensure the list is of type int
              .toList() ??
          [],
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
          [],
      analyticsViews: (json['analytics_views'] as num?)?.toInt() ?? 0,
    );
  }

  factory ContentItemModel.fromRecord(RecordModel record) {
    return ContentItemModel.fromJson(record.toJson());
  }

  // Helper method to safely parse DateTime from potentially empty or null strings
  static DateTime? _parseDateTime(dynamic value) {
    if (value == null) return null;
    if (value is String && value.isEmpty) return null;
    try {
      return DateTime.parse(value.toString());
    } catch (e) {
      // Log the error but don't crash the app
      LoggerService.warning('Error parsing DateTime: $value - $e');
      return null;
    }
  }

  // Helper to get the full image URL
  String? getImageUrl(PocketBase pb) {
    if (thumbnailImage == null || thumbnailImage!.isEmpty) {
      return null;
    }
    // Ensure collectionId and id are available and not empty
    if (collectionId.isEmpty || id.isEmpty) {
      return null;
    }
    try {
      // Use PocketBase SDK's helper if possible (requires RecordModel, so manual construction is safer here)
      // return pb.getFileUrl(record, thumbnailImage!).toString();
      // Manual construction:
      return Uri.parse(
        '${pb.baseURL}/api/files/$collectionId/$id/$thumbnailImage',
      ).toString();
    } catch (e) {
      // Log error but return null to prevent crashes
      LoggerService.warning("Error constructing image URL: $e");
      return null;
    }
  }
}
