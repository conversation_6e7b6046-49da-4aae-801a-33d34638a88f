<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="3Pay Global - Litigation funding platform connecting solicitors with co-funders">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">

  <!-- Content Security Policy for audio and media with CanvasKit support -->
  <meta http-equiv="Content-Security-Policy" content="
    default-src 'self' data: blob: 'unsafe-inline' 'unsafe-eval';
    script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: data: https://www.gstatic.com https://unpkg.com;
    style-src 'self' 'unsafe-inline' data: blob:;
    img-src 'self' data: blob: https: http:;
    media-src 'self' data: blob: https: http: *.3payglobal.com *.railway.app;
    connect-src 'self' data: blob: https: http: ws: wss: *.3payglobal.com *.railway.app https://www.gstatic.com https://unpkg.com;
    font-src 'self' data: blob: https: http:;
    object-src 'none';
    base-uri 'self';
    form-action 'self';
    worker-src 'self' blob:;
  ">

  <!-- Permissions Policy for audio features -->
  <meta http-equiv="Permissions-Policy" content="
    microphone=(),
    camera=(),
    geolocation=(),
    autoplay=(self),
    encrypted-media=(self)
  ">

  <!-- Audio and media support -->
  <meta name="audio-support" content="true">
  <meta name="media-support" content="audio/mpeg, audio/wav, audio/ogg, audio/mp4">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <meta name="apple-mobile-web-app-title" content="3Pay Global">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Android meta tags -->
  <meta name="theme-color" content="#000000">
  <meta name="mobile-web-app-capable" content="yes">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>3Pay Global</title>
  <link rel="manifest" href="manifest.json">

  <!-- Preload critical resources with proper attributes -->
  <link rel="preload" href="flutter_bootstrap.js" as="script">
  <link rel="modulepreload" href="main.dart.js">

  <!-- DNS prefetch for external resources -->
  <link rel="dns-prefetch" href="//www.gstatic.com">
  <link rel="dns-prefetch" href="//unpkg.com">

  <!-- Audio context initialization for better browser compatibility -->
  <script>
    // Initialize audio context early for better browser compatibility
    window.audioContextInitialized = false;

    function initializeAudioContext() {
      if (window.audioContextInitialized) return;

      try {
        // Create and resume audio context
        const AudioContext = window.AudioContext || window.webkitAudioContext;
        if (AudioContext) {
          const audioContext = new AudioContext();
          if (audioContext.state === 'suspended') {
            audioContext.resume();
          }
          window.audioContextInitialized = true;
          console.log('Audio context initialized successfully');
        }
      } catch (e) {
        console.warn('Audio context initialization failed:', e);
      }
    }

    // Initialize on user interaction
    document.addEventListener('click', initializeAudioContext, { once: true });
    document.addEventListener('touchstart', initializeAudioContext, { once: true });
    document.addEventListener('keydown', initializeAudioContext, { once: true });
  </script>
</head>
<body>
  <!-- Loading indicator -->
  <div id="loading" style="
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  ">
    <div style="text-align: center;">
      <div style="
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 16px;
      "></div>
      <div style="color: #666; font-size: 16px;">Loading 3Pay Global...</div>
    </div>
  </div>

  <style>
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>

  <!-- Flutter initialization with modern approach -->
  <script>
    // Prevent multiple initialization
    if (window.flutterInitialized) {
      console.log('Flutter already initialized, skipping...');
    } else {
      window.flutterInitialized = true;

      // Modern Flutter configuration
      const flutterConfig = {
        serviceWorkerSettings: {
          serviceWorkerVersion: null,
        },
        renderer: "html", // Force HTML renderer for better compatibility
        canvasKitBaseUrl: "https://unpkg.com/canvaskit-wasm@0.39.1/bin/",
        canvasKitVariant: "auto",
      };

      // Track loading state
      let isLoading = false;
      let bootstrapLoaded = false;

      // Error handling for resource loading
      window.addEventListener('error', function(e) {
        console.error('Resource loading error:', e);
        if (e.target && e.target.src && e.target.src.includes('canvaskit')) {
          console.warn('CanvasKit loading failed, falling back to HTML renderer');
          // Force HTML renderer if CanvasKit fails
          flutterConfig.renderer = "html";
        }
      });

      // Enhanced loading management
      function hideLoading() {
        const loading = document.getElementById('loading');
        if (loading) {
          loading.style.opacity = '0';
          loading.style.transition = 'opacity 0.3s ease';
          setTimeout(() => {
            loading.style.display = 'none';
          }, 300);
        }
      }

      // Listen for Flutter ready events
      window.addEventListener('flutter-first-frame', hideLoading);

      // Fallback to hide loading after 15 seconds with better error messaging
      setTimeout(function() {
        const loading = document.getElementById('loading');
        if (loading && loading.style.display !== 'none') {
          console.warn('Flutter app took longer than expected to load');
          hideLoading();
        }
      }, 15000);

      // Modern Flutter initialization
      function initializeFlutter() {
        if (isLoading) {
          console.log('Flutter initialization already in progress...');
          return;
        }
        isLoading = true;

        // Try modern approach first
        if (window.flutterBootstrap && bootstrapLoaded) {
          console.log('Using modern Flutter bootstrap');
          window.flutterBootstrap.initializeEngine(flutterConfig).then(function(engineInitializer) {
            return engineInitializer.initializeEngine();
          }).then(function(appRunner) {
            return appRunner.runApp();
          }).catch(function(error) {
            console.error('Modern Flutter initialization failed:', error);
            isLoading = false;
            fallbackToLegacy();
          });
        } else if (!bootstrapLoaded) {
          // Load flutter_bootstrap.js first
          loadFlutterBootstrap();
        }
      }

      // Fallback to legacy approach if modern fails
      function fallbackToLegacy() {
        if (document.querySelector('script[src="flutter.js"]')) {
          console.log('flutter.js already loaded, skipping fallback');
          return;
        }

        console.log('Falling back to legacy Flutter initialization');
        const script = document.createElement('script');
        script.src = 'flutter.js';
        script.async = true;
        script.onload = function() {
          console.log('Loaded flutter.js as fallback');
          if (window._flutter && window._flutter.loader) {
            window._flutter.loader.load(flutterConfig);
          }
        };
        script.onerror = function() {
          console.error('Failed to load flutter.js fallback');
          isLoading = false;
        };
        document.head.appendChild(script);
      }

      // Load Flutter bootstrap with error handling
      function loadFlutterBootstrap() {
        if (document.querySelector('script[src="flutter_bootstrap.js"]') || bootstrapLoaded) {
          console.log('flutter_bootstrap.js already loaded or loading');
          return;
        }

        const script = document.createElement('script');
        script.src = 'flutter_bootstrap.js';
        script.async = true;
        script.onload = function() {
          console.log('Loaded flutter_bootstrap.js successfully');
          bootstrapLoaded = true;
          initializeFlutter();
        };
        script.onerror = function() {
          console.error('Failed to load flutter_bootstrap.js');
          isLoading = false;
          fallbackToLegacy();
        };
        document.head.appendChild(script);
      }

      // Initialize Flutter loading (only once)
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
          setTimeout(initializeFlutter, 100); // Small delay to ensure DOM is ready
        });
      } else {
        setTimeout(initializeFlutter, 100);
      }
    }
  </script>
</body>
</html>
